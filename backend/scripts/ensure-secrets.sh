#!/bin/bash

# Script to ensure required secrets exist before Terraform deployment
# This prevents deployment failures when secrets don't exist
#
# Usage: ./ensure-secrets.sh [project_name] [environment]
# Example: ./ensure-secrets.sh gameflex staging
# Example: ./ensure-secrets.sh gameflex development
#
# This will create secrets with the naming pattern: {project_name}-{service}-config-{environment}
# For example: gameflex-twitch-config-staging

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if secret exists
secret_exists() {
    local secret_name=$1
    local region="${AWS_REGION:-${AWS_DEFAULT_REGION:-us-west-2}}"
    aws secretsmanager describe-secret --secret-id "$secret_name" --region "$region" &> /dev/null
}

# Function to create secret if it doesn't exist
ensure_secret() {
    local secret_name=$1
    local description=$2
    local secret_value=$3
    local region="${AWS_REGION:-${AWS_DEFAULT_REGION:-us-west-2}}"

    print_status "Using region: $region for secret operations"

    if secret_exists "$secret_name"; then
        print_success "Secret '$secret_name' already exists"
        return 0
    fi

    print_status "Creating secret '$secret_name' in region $region..."

    aws secretsmanager create-secret \
        --name "$secret_name" \
        --description "$description" \
        --secret-string "$secret_value" \
        --region "$region" > /dev/null

    if [ $? -eq 0 ]; then
        print_success "Created secret '$secret_name' in region $region"
    else
        print_error "Failed to create secret '$secret_name' in region $region"
        return 1
    fi
}

# Main function
main() {
    local project_name=${1:-gameflex}
    local environment=${2:-development}

    # Validate parameters
    if [[ -z "$project_name" || -z "$environment" ]]; then
        print_error "Invalid parameters provided"
        print_status "Usage: $0 [project_name] [environment]"
        print_status "Example: $0 gameflex staging"
        print_status "Example: $0 gameflex development"
        exit 1
    fi

    print_status "Ensuring secrets exist for project: $project_name, environment: $environment"
    print_status "Secret naming pattern: {project_name}-{service}-config-{environment}"

    # App Configuration Secret
    local app_config_secret_name="${project_name}-app-config-${environment}"
    local app_config_secret_value='{
        "cloudflareApiToken": "",
        "testUserEmail": "test@'${environment}'.gameflex.io",
        "testUserPassword": "Test123!",
        "debugMode": "'${environment}'",
        "apiBaseUrl": "",
        "userPoolId": "",
        "userPoolClientId": "",
        "xapiKey": ""
    }'
    
    ensure_secret "$app_config_secret_name" "General application configuration for GameFlex" "$app_config_secret_value"

    # Apple Sign In Configuration Secret
    local apple_config_secret_name="${project_name}-apple-config-${environment}"
    local apple_config_secret_value='{
        "teamId": "",
        "clientId": "",
        "keyId": "",
        "privateKey": ""
    }'

    ensure_secret "$apple_config_secret_name" "Apple Sign In configuration for GameFlex" "$apple_config_secret_value"

    # Xbox Integration Configuration Secret (Direct Azure)
    local xbox_config_secret_name="${project_name}-xbox-config-${environment}"
    local xbox_config_secret_value='{
        "azureClientId": "",
        "azureClientSecret": ""
    }'

    ensure_secret "$xbox_config_secret_name" "Xbox integration configuration for GameFlex" "$xbox_config_secret_value"

    # Twitch Integration Configuration Secret
    local twitch_config_secret_name="${project_name}-twitch-config-${environment}"
    local twitch_config_secret_value='{
        "clientId": "",
        "clientSecret": ""
    }'

    ensure_secret "$twitch_config_secret_name" "Twitch integration configuration for GameFlex" "$twitch_config_secret_value"

    # Kick Integration Configuration Secret
    local kick_config_secret_name="${project_name}-kick-config-${environment}"
    local kick_config_secret_value='{
        "clientId": "",
        "clientSecret": ""
    }'

    ensure_secret "$kick_config_secret_name" "Kick integration configuration for GameFlex" "$kick_config_secret_value"

    print_success "All required secrets are available"
    
    # Instructions for updating secrets
    print_warning "Remember to update the secrets with actual values:"
    echo ""
    echo "For app configuration:"
    echo "aws secretsmanager put-secret-value --secret-id '$app_config_secret_name' --secret-string '{\"cloudflareApiToken\":\"YOUR_CLOUDFLARE_TOKEN\",\"testUserEmail\":\"test@${environment}.gameflex.io\",\"testUserPassword\":\"Test123!\",\"debugMode\":\"${environment}\",\"apiBaseUrl\":\"YOUR_API_URL\",\"userPoolId\":\"YOUR_USER_POOL_ID\",\"userPoolClientId\":\"YOUR_USER_POOL_CLIENT_ID\",\"xapiKey\":\"YOUR_XAPI_US_API_KEY\"}'"
    echo ""
    echo "For Apple Sign In configuration:"
    echo "aws secretsmanager put-secret-value --secret-id '$apple_config_secret_name' --secret-string '{\"teamId\":\"YOUR_APPLE_TEAM_ID\",\"clientId\":\"YOUR_APPLE_CLIENT_ID\",\"keyId\":\"YOUR_APPLE_KEY_ID\",\"privateKey\":\"YOUR_APPLE_PRIVATE_KEY_CONTENT\"}'"
    echo ""
    echo "For Xbox integration configuration:"
    echo "aws secretsmanager put-secret-value --secret-id '$xbox_config_secret_name' --secret-string '{\"azureClientId\":\"YOUR_AZURE_CLIENT_ID\",\"azureClientSecret\":\"YOUR_AZURE_CLIENT_SECRET\"}'"
    echo ""
    echo "For Twitch integration configuration:"
    echo "aws secretsmanager put-secret-value --secret-id '$twitch_config_secret_name' --secret-string '{\"clientId\":\"YOUR_TWITCH_CLIENT_ID\",\"clientSecret\":\"YOUR_TWITCH_CLIENT_SECRET\"}'"
    echo ""
    echo "For Kick integration configuration:"
    echo "aws secretsmanager put-secret-value --secret-id '$kick_config_secret_name' --secret-string '{\"clientId\":\"YOUR_KICK_CLIENT_ID\",\"clientSecret\":\"YOUR_KICK_CLIENT_SECRET\"}'"
}

# Check if AWS CLI is available
if ! command -v aws &> /dev/null; then
    print_error "AWS CLI is not installed or not in PATH"
    exit 1
fi

# Check if AWS credentials are configured
if ! aws sts get-caller-identity &> /dev/null; then
    print_error "AWS credentials are not configured or invalid"
    exit 1
fi

# Run main function with provided arguments
main "$@"
